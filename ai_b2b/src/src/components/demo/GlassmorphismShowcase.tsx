'use client';

import React from 'react';
import { useTheme } from '../theme/ThemeProvider';

export function GlassmorphismShowcase() {
  const { effectiveTheme } = useTheme();

  const glassVariants = [
    {
      name: 'Primary Glass',
      className: 'glass-primary glass-interactive',
      description: 'KAUST Teal themed glass effect',
      icon: '🌊'
    },
    {
      name: 'Secondary Glass',
      className: 'glass-secondary glass-interactive',
      description: 'KAUST Gold themed glass effect',
      icon: '✨'
    },
    {
      name: 'Accent Glass',
      className: 'glass-accent glass-interactive',
      description: 'KAUST Orange themed glass effect',
      icon: '🔥'
    },
    {
      name: 'Neutral Glass',
      className: 'glass-neutral glass-interactive',
      description: 'KAUST Grey themed glass effect',
      icon: '🪨'
    }
  ];

  const intensityVariants = [
    {
      name: 'Light Glass',
      className: 'glass-light glass-interactive',
      description: 'Subtle transparency with light blur',
      blur: '10px'
    },
    {
      name: 'Medium Glass',
      className: 'glass-medium glass-interactive',
      description: 'Balanced transparency and blur',
      blur: '20px'
    },
    {
      name: 'Heavy Glass',
      className: 'glass-heavy glass-interactive',
      description: 'Strong transparency with heavy blur',
      blur: '30px'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Theme Info */}
      <div className="glass-card text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Glassmorphism Theme Showcase
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Current theme: <span className="font-semibold text-kaust-teal">{effectiveTheme.replace('glass-', '')}</span>
        </p>
        <div className="inline-flex items-center gap-2 glass-light px-4 py-2 rounded-lg">
          <div className="w-3 h-3 bg-kaust-teal rounded-full animate-pulse"></div>
          <span className="text-sm text-gray-700 dark:text-gray-300">Dynamic theme switching enabled</span>
        </div>
      </div>

      {/* Color Variants */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          KAUST Brand Color Variants
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {glassVariants.map((variant) => (
            <div key={variant.name} className={`${variant.className} p-6 rounded-2xl`}>
              <div className="text-center">
                <div className="text-3xl mb-3">{variant.icon}</div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  {variant.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {variant.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Intensity Variants */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Blur Intensity Variants
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {intensityVariants.map((variant) => (
            <div key={variant.name} className={`${variant.className} p-6 rounded-2xl`}>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 glass-primary rounded-full flex items-center justify-center">
                  <div className="w-8 h-8 bg-kaust-teal rounded-full"></div>
                </div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  {variant.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {variant.description}
                </p>
                <div className="inline-flex items-center gap-1 text-xs text-gray-500 dark:text-gray-500">
                  <span>Blur:</span>
                  <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{variant.blur}</code>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Interactive Elements */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Interactive Glass Elements
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="glass-card glass-bg-gradient-primary">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
              Gradient Background
            </h4>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Glass effect with KAUST brand gradient background
            </p>
            <button className="glass-interactive glass-heavy px-4 py-2 rounded-lg text-kaust-teal font-medium border border-kaust-teal/30">
              Hover me!
            </button>
          </div>
          
          <div className="glass-card glass-bg-gradient-warm">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
              Warm Gradient
            </h4>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Glass effect with warm orange-gold gradient
            </p>
            <button className="glass-interactive glass-heavy px-4 py-2 rounded-lg text-kaust-orange font-medium border border-kaust-orange/30">
              Click me!
            </button>
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="glass-card-lg glass-bg-gradient-cool">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Usage Examples
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">CSS Classes</h4>
            <div className="space-y-2 text-sm">
              <code className="block bg-gray-100 dark:bg-gray-800 p-2 rounded text-gray-800 dark:text-gray-200">
                .glass-primary
              </code>
              <code className="block bg-gray-100 dark:bg-gray-800 p-2 rounded text-gray-800 dark:text-gray-200">
                .glass-interactive
              </code>
              <code className="block bg-gray-100 dark:bg-gray-800 p-2 rounded text-gray-800 dark:text-gray-200">
                .glass-card-lg
              </code>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Theme Switching</h4>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <p>• Use data-theme attributes for manual control</p>
              <p>• Automatic dark/light mode detection</p>
              <p>• KAUST brand theme variant</p>
              <p>• Smooth transitions between themes</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
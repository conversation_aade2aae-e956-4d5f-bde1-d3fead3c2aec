'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

type GlassTheme = 'glass-light' | 'glass-dark' | 'glass-kaust' | 'auto';

interface ThemeContextType {
  theme: GlassTheme;
  setTheme: (theme: GlassTheme) => void;
  effectiveTheme: 'glass-light' | 'glass-dark' | 'glass-kaust';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: GlassTheme;
  storageKey?: string;
}

export function ThemeProvider({
  children,
  defaultTheme = 'auto',
  storageKey = 'glass-theme',
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<GlassTheme>(defaultTheme);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    const stored = localStorage.getItem(storageKey) as GlassTheme;
    if (stored) {
      setTheme(stored);
    }
    setMounted(true);
  }, [storageKey]);

  useEffect(() => {
    if (!mounted) return;
    
    const root = window.document.documentElement;
    
    // Remove previous theme classes
    root.removeAttribute('data-theme');
    root.classList.remove('glass-light', 'glass-dark', 'glass-kaust');
    
    let effectiveTheme: 'glass-light' | 'glass-dark' | 'glass-kaust';
    
    if (theme === 'auto') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'glass-dark'
        : 'glass-light';
      effectiveTheme = systemTheme;
    } else {
      effectiveTheme = theme;
    }
    
    root.setAttribute('data-theme', effectiveTheme);
    root.classList.add(effectiveTheme);
    
    localStorage.setItem(storageKey, theme);
  }, [theme, mounted, storageKey]);

  const handleSetTheme = (newTheme: GlassTheme) => {
    setTheme(newTheme);
  };

  const getEffectiveTheme = (): 'glass-light' | 'glass-dark' | 'glass-kaust' => {
    if (theme === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'glass-dark'
        : 'glass-light';
    }
    return theme;
  };

  const value = {
    theme,
    setTheme: handleSetTheme,
    effectiveTheme: mounted ? getEffectiveTheme() : 'glass-light',
  };

  if (!mounted) {
    return null;
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// Theme toggle component
export function ThemeToggle() {
  const { theme, setTheme, effectiveTheme } = useTheme();

  const themes: { value: GlassTheme; label: string; icon: string }[] = [
    { value: 'glass-light', label: 'Light Glass', icon: '☀️' },
    { value: 'glass-dark', label: 'Dark Glass', icon: '🌙' },
    { value: 'glass-kaust', label: 'KAUST Glass', icon: '🎨' },
    { value: 'auto', label: 'Auto', icon: '🔄' },
  ];

  return (
    <div className="glass-card-sm">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Theme:
        </span>
        <select
          value={theme}
          onChange={(e) => setTheme(e.target.value as GlassTheme)}
          className="glass-light px-3 py-1 text-sm rounded-lg border-0 focus:ring-2 focus:ring-kaust-teal bg-transparent"
        >
          {themes.map((t) => (
            <option key={t.value} value={t.value}>
              {t.icon} {t.label}
            </option>
          ))}
        </select>
        <div className="text-xs text-gray-500">
          Active: {effectiveTheme.replace('glass-', '')}
        </div>
      </div>
    </div>
  );
}
import React from 'react';
import Header from './Header';
import Footer from './Footer';
import MainContent from './MainContent';

interface PortalLayoutProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

export default function PortalLayout({ 
  children, 
  className = '', 
  showHeader = true, 
  showFooter = true 
}: PortalLayoutProps) {
  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {showHeader && <Header />}
      <MainContent className="flex-1">
        {children}
      </MainContent>
      {showFooter && <Footer />}
    </div>
  );
}
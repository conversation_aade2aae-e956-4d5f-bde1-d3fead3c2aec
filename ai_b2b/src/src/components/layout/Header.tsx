import React from 'react';

interface HeaderProps {
  className?: string;
}

export default function Header({ className = '' }: HeaderProps) {
  return (
    <header className={`glass-medium sticky top-4 z-50 mx-4 rounded-2xl ${className}`}>
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-36 glass-primary rounded-xl flex items-center justify-center border border-kaust-teal/30">
                <span className="text-kaust-teal font-bold text-lg tracking-wide">KAUST</span>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-6">
            <a href="#" className="glass-light px-4 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:text-kaust-teal transition-colors font-medium">
              Products
            </a>
            <a href="#" className="glass-light px-4 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:text-kaust-teal transition-colors font-medium">
              Solutions
            </a>
            <a href="#" className="glass-light px-4 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:text-kaust-teal transition-colors font-medium">
              Support
            </a>
          </nav>

          {/* Search */}
          <div className="hidden lg:flex items-center">
            <div className="glass-light rounded-xl border border-white/20">
              <input 
                type="text" 
                placeholder="Search products..."
                className="h-10 w-64 px-4 bg-transparent text-gray-700 dark:text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-kaust-teal/50 rounded-xl"
              />
            </div>
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-3">
            <button className="glass-interactive glass-light p-2 rounded-xl text-gray-600 dark:text-gray-400 hover:text-kaust-teal">
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </button>
            <button className="glass-interactive glass-light p-2 rounded-xl text-gray-600 dark:text-gray-400 hover:text-kaust-teal">
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </button>
            <button className="glass-interactive glass-secondary px-4 py-2 rounded-xl text-kaust-gold font-medium border border-kaust-gold/30">
              Sign In
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="glass-interactive glass-light p-2 rounded-xl text-gray-600 dark:text-gray-400 hover:text-kaust-teal focus:outline-none focus:ring-2 focus:ring-kaust-teal/50"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              <svg
                className="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
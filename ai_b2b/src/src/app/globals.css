@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* KAUST Official Brand Colors */
  --kaust-gold: #F9C217;
  --kaust-orange: #EF8721;
  --kaust-teal: #1F9EA8;
  --kaust-grey: #958B83;
  --kaust-blue: #0077b6;
  --kaust-navy: #023e8a;

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: rgba(0, 0, 0, 0.1);
  --glass-backdrop-blur: 20px;
  --glass-border-radius: 16px;

  --glass-primary: rgba(31, 158, 168, 0.15);
  --glass-secondary: rgba(249, 194, 23, 0.15);
  --glass-accent: rgba(239, 135, 33, 0.15);
  --glass-neutral: rgba(149, 139, 131, 0.15);

  /* Light Theme */
  --background: #ffffff;
  --foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #475569;
  --border: #e2e8f0;
  --input: #ffffff;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --primary: var(--kaust-teal);
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: var(--kaust-gold);
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: var(--kaust-teal);
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --border: #334155;
    --input: #1e293b;
    --card: #0f172a;
    --card-foreground: #ededed;
    --primary: var(--kaust-teal);
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;
    --accent: var(--kaust-gold);
    --accent-foreground: #0f172a;
    --destructive: #dc2626;
    --destructive-foreground: #ffffff;
    --ring: var(--kaust-teal);

    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);
    --glass-primary: rgba(31, 158, 168, 0.2);
    --glass-secondary: rgba(249, 194, 23, 0.2);
    --glass-accent: rgba(239, 135, 33, 0.2);
    --glass-neutral: rgba(149, 139, 131, 0.2);
  }
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-kaust-teal focus:ring-offset-2;
}

.text-muted-foreground {
  color: var(--muted-foreground);
}

@media print {
  .no-print {
    display: none !important;
  }
}

/* KAUST Colors */
@layer utilities {
  .bg-kaust-gold     { background-color: var(--kaust-gold); }
  .bg-kaust-orange   { background-color: var(--kaust-orange); }
  .bg-kaust-teal     { background-color: var(--kaust-teal); }
  .bg-kaust-grey     { background-color: var(--kaust-grey); }

  .text-kaust-gold   { color: var(--kaust-gold); }
  .text-kaust-orange { color: var(--kaust-orange); }
  .text-kaust-teal   { color: var(--kaust-teal); }
  .text-kaust-grey   { color: var(--kaust-grey); }

  .border-kaust-gold   { border-color: var(--kaust-gold); }
  .border-kaust-orange { border-color: var(--kaust-orange); }
  .border-kaust-teal   { border-color: var(--kaust-teal); }
  .border-kaust-grey   { border-color: var(--kaust-grey); }

  .kaust-gradient {
    background: linear-gradient(135deg, var(--kaust-blue) 0%, var(--kaust-navy) 100%);
  }

  .kaust-text-gradient {
    background: linear-gradient(135deg, var(--kaust-blue) 0%, var(--kaust-navy) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .hero-gradient {
    background: linear-gradient(135deg, var(--kaust-teal) 0%, var(--kaust-orange) 100%);
  }

  /* Glassmorphism */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-backdrop-blur));
    -webkit-backdrop-filter: blur(var(--glass-backdrop-blur));
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: 0 8px 32px var(--glass-shadow);
    position: relative;
    overflow: hidden;
  }

  .glass::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; height: 1px;
    background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
    opacity: 0.5;
  }

  .glass-primary   { background: var(--glass-primary); border-color: rgba(31,158,168,0.3); }
  .glass-secondary { background: var(--glass-secondary); border-color: rgba(249,194,23,0.3); }
  .glass-accent    { background: var(--glass-accent); border-color: rgba(239,135,33,0.3); }
  .glass-neutral   { background: var(--glass-neutral); border-color: rgba(149,139,131,0.3); }

  .glass-light {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .glass-medium {
    background: var(--glass-bg);
  }

  .glass-heavy {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(30px);
  }

  .glass-interactive {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
  }

  .glass-interactive:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px var(--glass-shadow);
    transform: translateY(-2px);
  }

  .glass-interactive:active {
    transform: translateY(0);
    box-shadow: 0 4px 16px var(--glass-shadow);
  }

  .glass-card      { @apply glass p-6; }
  .glass-card-sm   { @apply glass p-4 rounded-[12px]; }
  .glass-card-lg   { @apply glass p-8 rounded-[24px]; }

  [data-theme="glass-light"] {
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: rgba(0, 0, 0, 0.1);
  }

  [data-theme="glass-dark"] {
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);
  }

  [data-theme="glass-kaust"] {
    --glass-bg: rgba(31, 158, 168, 0.1);
    --glass-border: rgba(31, 158, 168, 0.2);
    --glass-shadow: rgba(31, 158, 168, 0.2);
  }

  .glass-bg-gradient-primary {
    background: linear-gradient(135deg, rgba(31, 158, 168, 0.1), rgba(249, 194, 23, 0.1));
  }

  .glass-bg-gradient-warm {
    background: linear-gradient(135deg, rgba(239, 135, 33, 0.1), rgba(249, 194, 23, 0.1));
  }

  .glass-bg-gradient-cool {
    background: linear-gradient(135deg, rgba(31, 158, 168, 0.1), rgba(149, 139, 131, 0.1));
  }

  @media (prefers-reduced-motion: reduce) {
    .glass-interactive {
      transition: none;
    }
  }

  @media (max-width: 768px) {
    .glass {
      backdrop-filter: blur(10px);
    }
  }

  @media (max-width: 480px) {
    .glass-card { @apply p-4 rounded-[12px]; }
    .glass-card-lg { @apply p-6 rounded-[16px]; }
  }
}

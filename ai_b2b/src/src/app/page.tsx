import { PortalLayout, PageContainer } from '@/components/layout';
import { ThemeToggle } from "../components/theme/ThemeProvider";
import { GlassmorphismShowcase } from '../components/demo/GlassmorphismShowcase';

export default function Home() {
  return (
    <PortalLayout>
      <PageContainer 
        title="Welcome to KAUST B2B Marketplace" 
        subtitle="Your gateway to scientific and technological solutions"
      >
        <div className="space-y-8">
          {/* Theme Toggle */}
          <div className="flex justify-end">
            <ThemeToggle />
          </div>
          
          {/* Hero Section */}
          <div className="glass-card-lg glass-bg-gradient-primary text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-kaust-teal/30 to-kaust-orange/30 -z-10"></div>
            <div className="max-w-3xl relative z-10">
              <h2 className="text-3xl font-bold mb-4 text-white drop-shadow-lg">
                Empowering Innovation Through Collaboration
              </h2>
              <p className="text-lg opacity-95 mb-6 text-white/90">
                Connect with leading suppliers, discover cutting-edge products, and streamline your procurement process with KAUST's comprehensive B2B marketplace.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="glass-interactive glass-heavy text-kaust-teal px-6 py-3 rounded-xl font-semibold border border-white/30">
                  Explore Products
                </button>
                <button className="glass-interactive glass-light border-2 border-white/50 text-white px-6 py-3 rounded-xl font-semibold">
                  Learn More
                </button>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="glass-card glass-primary glass-interactive">
              <div className="w-12 h-12 glass-light rounded-xl flex items-center justify-center mb-4 border border-kaust-teal/30">
                <div className="w-6 h-6 bg-kaust-teal rounded-lg shadow-lg"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Product Catalog
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Browse our comprehensive catalog of scientific equipment, research tools, and technological solutions.
              </p>
            </div>

            <div className="glass-card glass-secondary glass-interactive">
              <div className="w-12 h-12 glass-light rounded-xl flex items-center justify-center mb-4 border border-kaust-gold/30">
                <div className="w-6 h-6 bg-kaust-gold rounded-lg shadow-lg"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Order Management
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Streamline your procurement process with our integrated order and quotation management system.
              </p>
            </div>

            <div className="glass-card glass-accent glass-interactive">
              <div className="w-12 h-12 glass-light rounded-xl flex items-center justify-center mb-4 border border-kaust-orange/30">
                <div className="w-6 h-6 bg-kaust-orange rounded-lg shadow-lg"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Enterprise Solutions
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Access enterprise-grade features designed for academic and research institutions.
              </p>
            </div>
          </div>

          {/* Status Section */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Implementation Status
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Basic Layout Structure</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  ✓ Completed
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">State Management Foundation</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  ⏳ Next Phase
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Authentication System</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  📋 Planned
                </span>
              </div>
            </div>
          </div>

          {/* Glassmorphism Showcase Section */}
          <div className="py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <GlassmorphismShowcase />
            </div>
          </div>
        </div>
      </PageContainer>
    </PortalLayout>
  );
}

import type { Metadata, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "../components/theme/ThemeProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: {
    default: "KAUST B2B Marketplace",
    template: "%s | KAUST B2B Marketplace",
  },
  description: "King Abdullah University of Science and Technology B2B Marketplace - Your gateway to scientific and technological solutions",
  keywords: ["KAUST", "B2B", "marketplace", "science", "technology", "university"],
  authors: [{ name: "KAUS<PERSON>" }],
  creator: "<PERSON>AUS<PERSON>",
  publisher: "KAUST",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://marketplace.kaust.edu.sa",
    title: "KAUST B2B Marketplace",
    description: "King Abdullah University of Science and Technology B2B Marketplace",
    siteName: "KAUST B2B Marketplace",
  },
  twitter: {
    card: "summary_large_image",
    title: "KAUST B2B Marketplace",
    description: "King Abdullah University of Science and Technology B2B Marketplace",
  },

};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen`}
        suppressHydrationWarning={true}
      >
        <div className="min-h-screen bg-gradient-to-br from-kaust-teal/20 via-kaust-gold/10 to-kaust-orange/20">
          <ThemeProvider defaultTheme="glass-light">
            {children}
          </ThemeProvider>
        </div>
      </body>
    </html>
  );
}
